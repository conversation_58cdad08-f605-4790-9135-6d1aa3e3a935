import React, { useState, useMemo, useEffect } from "react";
import { useGetApiQuery, usePostApiMutation } from "@/store/api/master/commonSlice";
import { FaStar, FaSearch, FaTimes } from "react-icons/fa";
import { X } from "lucide-react";
import { DEFAULT_USER_ICON } from "@/config";
import { toast } from "react-toastify";

const Sellers = ({ requestId, assignedSellers, onClose }) => {
  console.log(assignedSellers);
  // API hooks
  const {
    data: sellerData,
    isLoading,
    isError
  } = useGetApiQuery(`/admin/users?role=Seller`);

  const [postApi, { isLoading: isSubmitting }] = usePostApiMutation();

  // State
  const [notes, setNotes] = useState("");
  const [selectedSellers, setSelectedSellers] = useState([]);
  const [selectAll, setSelectAll] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");

  // Filter out already assigned sellers from the available sellers list
  const availableSellers = useMemo(() => {
    if (!sellerData?.data) return [];

    const assignedSellerIds = assignedSellers?.map(assignment => assignment.seller.id) || [];
    return sellerData.data.filter(seller => !assignedSellerIds.includes(seller.id));
  }, [sellerData?.data, assignedSellers]);

  // Filter sellers based on search term and exclude selected ones
  const filteredSellers = useMemo(() => {
    return availableSellers.filter(seller => {
      const matchesSearch = searchTerm === '' || 
        `${seller.first_name} ${seller.last_name}`.toLowerCase().includes(searchTerm.toLowerCase());
      
      const isSelected = selectedSellers.some(s => s.id === seller.id);
      
      return matchesSearch && !isSelected;
    });
  }, [availableSellers, searchTerm, selectedSellers]);

  const isSelected = (id) => selectedSellers.some((s) => s.id === id);

  const handleToggleSelect = (seller) => {
    const isAlreadySelected = isSelected(seller.id);
    const updatedSelection = isAlreadySelected
      ? selectedSellers.filter((s) => s.id !== seller.id)
      : [...selectedSellers, seller];

    setSelectedSellers(updatedSelection);
  };

  const handleToggleSelectAll = () => {
    if (selectAll) {
      setSelectedSellers([]);
    } else {
      setSelectedSellers(filteredSellers);
    }
    setSelectAll(!selectAll);
  };

  // Keep selectAll state in sync with selection
  useEffect(() => {
    setSelectAll(filteredSellers.length > 0 && selectedSellers.length === filteredSellers.length);
  }, [selectedSellers, filteredSellers]);

  // Handle form submission
  const handleSubmit = async () => {
    if (selectedSellers.length === 0) {
      toast.error("Please select at least one seller");
      return;
    }

    if (!requestId) {
      toast.error("Request ID is missing");
      return;
    }

    try {
      const payload = {
        request_id: requestId,
        seller_ids: selectedSellers.map(seller => seller.id),
        notes: notes.trim() || ''
      };

      const response = await postApi({
        end_point: 'admin/requests/assign-sellers',
        body: payload
      });

      if (response.error) {
        toast.error(response.error.data?.message || "Failed to assign sellers");
        console.error("Error assigning sellers:", response.error);
      } else {
        toast.success("Sellers assigned successfully");
        onClose();
      }
    } catch (error) {
      toast.error("An unexpected error occurred");
      console.error("Error assigning sellers:", error);
    }
  };

  if (isLoading) {
    return (
      <div className="p-6 bg-white rounded-lg shadow-sm mt-6 flex justify-center items-center h-64">
        <div className="text-center">
          <svg className="animate-spin h-10 w-10 text-indigo-600 mx-auto" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <p className="mt-4 text-gray-600">Loading sellers...</p>
        </div>
      </div>
    );
  }

  if (isError) {
    return (
      <div className="p-6 bg-white rounded-lg shadow-sm mt-6">
        <div className="bg-red-50 p-4 rounded-md border border-red-100">
          <h3 className="text-red-800 font-medium flex items-center gap-2">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
            Error Loading Sellers
          </h3>
          <p className="text-red-600 mt-2">Unable to load sellers. Please try again later.</p>
          <button
            className="mt-4 bg-red-100 text-red-800 px-4 py-2 rounded-md hover:bg-red-200 transition-colors text-sm font-medium"
            onClick={onClose}
          >
            Close
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 bg-white rounded-lg shadow-sm">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h2 className="text-xl font-semibold text-gray-800">Assign Sellers</h2>
          <p className="text-sm text-gray-500 mt-1">Select sellers to assign to this request</p>
        </div>
        <button 
          onClick={onClose} 
          className="text-gray-400 hover:text-gray-600 transition-colors p-1 rounded-full hover:bg-gray-100"
          aria-label="Close"
        >
          <X size={20} />
        </button>
      </div>

      {/* Search and Filters */}
      <div className="mb-6">
        <div className="relative mb-4">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <FaSearch className="text-gray-400" />
          </div>
          <input
            type="text"
            placeholder="Search sellers by name..."
            className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
          {searchTerm && (
            <button
              onClick={() => setSearchTerm("")}
              className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
            >
              <FaTimes />
            </button>
          )}
        </div>

        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <select className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-indigo-500 focus:border-indigo-500">
              <option>Filter by category</option>
            </select>
            <select className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-indigo-500 focus:border-indigo-500">
              <option>Filter by subcategory</option>
            </select>
          </div>
          
          {filteredSellers.length > 0 && (
            <label className="flex items-center space-x-2 text-sm text-gray-600 cursor-pointer">
              <input
                type="checkbox"
                checked={selectAll}
                onChange={handleToggleSelectAll}
                className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
              />
              <span>Select all ({filteredSellers.length})</span>
            </label>
          )}
        </div>
      </div>

      {/* Selected Sellers */}
      {selectedSellers.length > 0 && (
        <div className="mb-6">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-sm font-medium text-gray-700">Selected Sellers</h3>
            <span className="text-xs bg-indigo-100 text-indigo-800 px-2 py-1 rounded-full">
              {selectedSellers.length} selected
            </span>
          </div>
          <div className="border border-gray-200 rounded-lg p-3 bg-gray-50 flex flex-wrap gap-3">
            {selectedSellers.map((seller) => (
              <div
                key={seller.id}
                className="flex items-center gap-3 border border-indigo-200 rounded-lg p-2 bg-white shadow-xs w-full sm:w-auto cursor-pointer hover:border-indigo-300 transition-colors"
                onClick={() => handleToggleSelect(seller)}
              >
                <input
                  type="checkbox"
                  checked
                  onChange={(e) => {
                    e.stopPropagation();
                    handleToggleSelect(seller);
                  }}
                  className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded cursor-pointer"
                />
                <img
                  src={seller.profile_picture_url
                    ? import.meta.env.VITE_ASSET_HOST_URL + seller.profile_picture_url
                    : DEFAULT_USER_ICON
                  }
                  alt={seller.first_name}
                  className="w-8 h-8 rounded-full object-cover"
                />
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-800 truncate">
                    {seller.first_name} {seller.last_name}
                  </p>
                  <div className="flex items-center">
                    <div className="flex text-yellow-400 mr-2">
                      {[...Array(5)].map((_, i) => (
                        <FaStar key={i} size={10} />
                      ))}
                    </div>
                    <span className="text-xs text-gray-500">5.0</span>
                  </div>
                </div>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleToggleSelect(seller);
                  }}
                  className="text-gray-400 hover:text-red-500 transition-colors p-1"
                  aria-label="Remove seller"
                >
                  <X size={16} />
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Seller List */}
      <div className="mb-6">
        <h3 className="text-sm font-medium text-gray-700 mb-3">Available Sellers</h3>
        {filteredSellers.length > 0 ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 max-h-96 overflow-y-auto p-1">
            {filteredSellers.map((seller) => (
              <div
                key={seller.id}
                className={`flex items-center gap-3 border rounded-lg p-3 cursor-pointer transition-all ${
                  isSelected(seller.id)
                    ? 'border-indigo-300 bg-indigo-50'
                    : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                }`}
                onClick={() => handleToggleSelect(seller)}
              >
                <input
                  type="checkbox"
                  checked={isSelected(seller.id)}
                  onChange={(e) => {
                    e.stopPropagation();
                    handleToggleSelect(seller);
                  }}
                  className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded cursor-pointer"
                />
                <img
                  src={seller.profile_picture_url
                    ? import.meta.env.VITE_ASSET_HOST_URL + seller.profile_picture_url
                    : DEFAULT_USER_ICON
                  }
                  alt={seller.first_name}
                  className="w-10 h-10 rounded-full object-cover"
                />
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-800 truncate">
                    {seller.first_name} {seller.last_name}
                  </p>
                  <div className="flex items-center">
                    <div className="flex text-yellow-400 mr-2">
                      {[...Array(5)].map((_, i) => (
                        <FaStar key={i} size={10} />
                      ))}
                    </div>
                    <span className="text-xs text-gray-500">5.0</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="border border-gray-200 rounded-lg p-8 text-center bg-gray-50">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <h4 className="mt-3 text-sm font-medium text-gray-700">
              {searchTerm ? 'No sellers match your search' : 'No sellers available'}
            </h4>
            <p className="mt-1 text-xs text-gray-500">
              {searchTerm ? 'Try a different search term' : 'All available sellers have been assigned'}
            </p>
          </div>
        )}
      </div>

      {/* Notes */}
      <div className="mb-6">
        <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-2">
          Assignment Notes
          <span className="text-gray-400 ml-1 font-normal">(optional)</span>
        </label>
        <textarea
          id="notes"
          rows={3}
          className="w-full border border-gray-300 rounded-md p-3 text-sm focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
          placeholder="Add any notes about this assignment that the sellers should know..."
          value={notes}
          onChange={(e) => setNotes(e.target.value)}
        />
      </div>

      {/* Footer */}
      <div className="flex justify-end gap-3 pt-4 border-t border-gray-200">
        <button
          className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors"
          onClick={onClose}
          disabled={isSubmitting}
        >
          Cancel
        </button>
        <button
          className={`px-4 py-2 rounded-md text-sm font-medium text-white flex items-center justify-center ${
            isSubmitting || selectedSellers.length === 0
              ? 'bg-indigo-400 cursor-not-allowed'
              : 'bg-indigo-600 hover:bg-indigo-700 transition-colors'
          }`}
          onClick={handleSubmit}
          disabled={isSubmitting || selectedSellers.length === 0}
        >
          {isSubmitting ? (
            <>
              <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Assigning...
            </>
          ) : (
            `Assign Seller${selectedSellers.length !== 1 ? 's' : ''} (${selectedSellers.length})`
          )}
        </button>
      </div>
    </div>
  );
};

export default Sellers;