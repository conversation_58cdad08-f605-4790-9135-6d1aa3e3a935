import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useGetApiQuery } from '@/store/api/master/commonSlice';
import { skipToken } from '@reduxjs/toolkit/query';
import Button from '@/components/ui/Button';
import Card from '@/components/ui/Card';
import Badge from '@/components/ui/Badge';
import Icon from '@/components/ui/Icon';
import Loading from '@/components/Loading';
import Sellers from "./Sellers";

const MergedRequestDetails = () => {
  const { id } = useParams();
  const navigate = useNavigate();

  const {
    data: requestData,
    isLoading: isRequestLoading,
    isError: isRequestError,
  } = useGetApiQuery(id ? `/admin/requests/${id}` : skipToken);

  const formatDate = (dateString) => {
    return dateString
      ? new Date(dateString).toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric',
        })
      : 'N/A';
  };

  const formatCurrency = (min, max) => {
    if (!min && !max) return 'N/A';
    if (min && max) return `$${min} - $${max}`;
    if (min) return `$${min}+`;
    return `Up to $${max}`;
  };

  const getBadgeColor = (type, value) => {
    const map = {
      urgency: {
        urgent: 'bg-red-500 text-white',
        high: 'bg-orange-500 text-white',
        normal: 'bg-blue-500 text-white',
        low: 'bg-green-500 text-white',
      },
      status: {
        merged: 'bg-purple-500 text-white',
        pending: 'bg-yellow-600 text-white',
        approved: 'bg-green-500 text-white',
        rejected: 'bg-red-500 text-white',
      },
    };
    return map[type]?.[value?.toLowerCase()] || 'bg-gray-500 text-white';
  };

  if (isRequestLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <Loading />
      </div>
    );
  }

  if (isRequestError || !requestData?.data) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen text-center px-4">
        <Icon icon="heroicons:exclamation-triangle" className="w-16 h-16 text-red-500 mb-4" />
        <h2 className="text-xl font-semibold text-gray-900 mb-2">Request Not Found</h2>
        <p className="text-gray-600 mb-4">
          The merged request you're looking for doesn't exist or has been removed.
        </p>
        <Button variant="primary" onClick={() => navigate('/requests')}>
          Back to Requests
        </Button>
      </div>
    );
  }

  const request = requestData.data;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-wrap items-center justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Merged Request Details</h1>
          <p className="text-sm text-gray-600">
            Comprehensive view of the merged and original requests
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => navigate('/requests')}>
            <Icon icon="heroicons:arrow-left" className="mr-2 w-4 h-4" /> Back
          </Button>
          {/* {request.is_merged && (
            <Button variant="primary" onClick={() => navigate(`/request/edit/${request.id}`)}>
              <Icon icon="heroicons:pencil-square" className="mr-2 w-4 h-4" /> Edit
            </Button>
          )} */}
        </div>
      </div>

      {/* Main Request Info */}
      <Card className="p-6 space-y-6">
        <div className="flex flex-wrap items-center justify-between gap-4">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">{request.title}</h2>
            <p className="text-sm text-gray-600">Code: {request.request_code}</p>
          </div>
          <div className="flex gap-2">
            <Badge className={getBadgeColor('status', request.status)}>{request.status}</Badge>
            {/* <Badge className={getBadgeColor('urgency', request.urgency)}>{request.urgency}</Badge> */}
          </div>
        </div>

        {/* Grid Info */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* Buyer Info */}
          <div>
            <h3 className="text-sm font-medium text-gray-700 mb-1">Buyer</h3>
            <div className="flex items-center">
              {request.buyer.profile_picture_url && (
                <img
                  src={request.buyer.profile_picture_url}
                  alt="Buyer"
                  className="w-10 h-10 rounded-full mr-2"
                />
              )}
              <div>
                <p className="text-sm font-medium text-gray-900">
                  {request.buyer.first_name} {request.buyer.last_name}
                </p>
                <p className="text-xs text-gray-600">{request.buyer.email}</p>
              </div>
            </div>
          </div>

          <div>
            <h3 className="text-sm font-medium text-gray-700 mb-1">Category</h3>
            <p className="text-sm text-gray-900">{request.category.title}</p>
          </div>

          <div>
            <h3 className="text-sm font-medium text-gray-700 mb-1">Sub Category</h3>
            <p className="text-sm text-gray-900">{request.sub_category.title}</p>
          </div>

          <div>
            <h3 className="text-sm font-medium text-gray-700 mb-1">Request Type</h3>
            <p className="text-sm text-gray-900">{request.request_type}</p>
          </div>

          <div>
            <h3 className="text-sm font-medium text-gray-700 mb-1">Quantity</h3>
            <p className="text-sm text-gray-900">{request.quantity}</p>
          </div>

          <div>
            <h3 className="text-sm font-medium text-gray-700 mb-1">Budget</h3>
            <p className="text-sm text-gray-900">
              {formatCurrency(request.budget_min, request.budget_max)}
            </p>
          </div>

          <div>
            <h3 className="text-sm font-medium text-gray-700 mb-1">Deadline</h3>
            <p className="text-sm text-gray-900">{formatDate(request.deadline)}</p>
          </div>

          {request.location && (
            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-1">Location</h3>
              <p className="text-sm text-gray-900">{request.location}</p>
            </div>
          )}

          <div>
            <h3 className="text-sm font-medium text-gray-700 mb-1">Created</h3>
            <p className="text-sm text-gray-900">{formatDate(request.created_at)}</p>
          </div>

          <div>
            <h3 className="text-sm font-medium text-gray-700 mb-1">Last Updated</h3>
            <p className="text-sm text-gray-900">{formatDate(request.updated_at)}</p>
          </div>

          {request.request_statuses?.[0] && (
            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-1">Status Updated By</h3>
              <p className="text-sm text-gray-900">
                {request.request_statuses[0].updated_by_user.first_name}{' '}
                {request.request_statuses[0].updated_by_user.last_name}
              </p>
              {request.request_statuses[0].reason && (
                <p className="text-xs text-gray-600 mt-1">
                  {request.request_statuses[0].reason}
                </p>
              )}
            </div>
          )}
        </div>

        {/* Descriptions */}
        {['short_description', 'description', 'additional_info'].map((field) =>
          request[field] ? (
            <div key={field}>
              <h3 className="text-sm font-medium text-gray-700 mb-1 capitalize">
                {field.replace(/_/g, ' ')}
              </h3>
              <div className="bg-gray-50 text-sm text-gray-900 p-4 rounded-md whitespace-pre-wrap">
                {request[field]}
              </div>
            </div>
          ) : null
        )}
      </Card>

      {/* Merged Requests */}
      {request.merged_requests?.length > 0 && (
        <Card className="p-6 space-y-4">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold text-gray-900">
              Original Requests ({request.merged_requests.length})
            </h2>
            <Badge className="bg-purple-100 text-purple-800">
              Merged from {request.merged_requests.length} requests
            </Badge>
          </div>

          {request.merged_requests.map(({ id, merged_item, merged_by_user }) => (
            <Card key={id} className="p-4 border border-gray-200 mb-4">
              <div className="flex flex-wrap justify-between gap-4 ">
                <div className="flex-1 space-y-2">
                  <div className="flex flex-wrap items-center gap-3">
                    <h5 className="font-medium text-gray-900">{merged_item.title}</h5>
                 
                    <Badge className={getBadgeColor('urgency', merged_item.urgency)}>
                      {merged_item.urgency}
                    </Badge>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2 text-sm">
                    <p><span className="font-medium">Buyer:</span> {merged_item.buyer.first_name} {merged_item.buyer.last_name}</p>
                    <p><span className="font-medium">Category:</span> {merged_item.category.title}</p>
                    <p><span className="font-medium">Sub Category:</span> {merged_item.sub_category.title}</p>
                    <p><span className="font-medium">Budget:</span> {formatCurrency(merged_item.budget_min, merged_item.budget_max)}</p>
                  </div>

                  {merged_item.short_description && (
                    <p className="text-sm text-gray-700">
                      <span className="font-medium">Description:</span> {merged_item.short_description}
                    </p>
                  )}

                
                </div>

                <div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => navigate(`/request/${merged_item.id}`)}
                  >
                    View Details
                  </Button>
                </div>
              </div>
            </Card>
          ))}
        </Card>
      )}


      <Sellers requestId={request.id} assignedSellers={request.assigned_sellers}/>

    </div>
  );
};

export default MergedRequestDetails;